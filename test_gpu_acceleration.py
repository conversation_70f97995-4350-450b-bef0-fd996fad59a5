#!/usr/bin/env python3
"""
Test script to verify GPU acceleration is working properly
"""

import torch
import time
from textureGenPipeline import MaterialMVPConfig

def test_gpu_setup():
    """Test GPU availability and configuration"""
    print("=== GPU Acceleration Test ===")
    
    # Test CUDA availability
    print(f"CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"GPU Count: {torch.cuda.device_count()}")
        print(f"Current GPU: {torch.cuda.current_device()}")
        print(f"GPU Name: {torch.cuda.get_device_name()}")
        
        # Test GPU memory
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"Total GPU Memory: {total_memory:.1f} GB")
        
        # Test tensor operations on GPU
        print("\n=== Testing GPU Tensor Operations ===")
        device = torch.device("cuda")
        
        # Create test tensors
        size = 1000
        a = torch.randn(size, size, device=device)
        b = torch.randn(size, size, device=device)
        
        # Time matrix multiplication
        start_time = time.time()
        c = torch.matmul(a, b)
        torch.cuda.synchronize()  # Wait for GPU operation to complete
        gpu_time = time.time() - start_time
        
        print(f"GPU Matrix Multiplication ({size}x{size}): {gpu_time:.4f}s")
        
        # Compare with CPU
        a_cpu = a.cpu()
        b_cpu = b.cpu()
        start_time = time.time()
        c_cpu = torch.matmul(a_cpu, b_cpu)
        cpu_time = time.time() - start_time
        
        print(f"CPU Matrix Multiplication ({size}x{size}): {cpu_time:.4f}s")
        print(f"GPU Speedup: {cpu_time/gpu_time:.1f}x")
        
    else:
        print("❌ CUDA not available - GPU acceleration disabled")
        return False
    
    # Test MaterialMVP configuration
    print("\n=== Testing MaterialMVP Configuration ===")
    try:
        config = MaterialMVPConfig(max_num_view=6, resolution=512)
        print(f"✓ MaterialMVP Config created successfully")
        print(f"✓ Device: {config.device}")
        return True
    except Exception as e:
        print(f"❌ Error creating MaterialMVP Config: {e}")
        return False

def test_model_loading():
    """Test if models can be loaded on GPU"""
    print("\n=== Testing Model Loading ===")
    
    try:
        from utils.image_super_utils import imageSuperNet
        config = MaterialMVPConfig(max_num_view=6, resolution=512)
        
        print("Testing RealESRGAN model loading...")
        start_time = time.time()
        super_model = imageSuperNet(config)
        load_time = time.time() - start_time
        print(f"✓ RealESRGAN loaded in {load_time:.1f}s")
        
        return True
    except Exception as e:
        print(f"❌ Error loading models: {e}")
        return False

if __name__ == "__main__":
    gpu_ok = test_gpu_setup()
    if gpu_ok:
        model_ok = test_model_loading()
        if model_ok:
            print("\n✅ All tests passed! GPU acceleration should work properly.")
        else:
            print("\n⚠️  GPU works but model loading failed.")
    else:
        print("\n❌ GPU acceleration not available.")
