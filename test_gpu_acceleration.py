#!/usr/bin/env python3
"""
Test script to verify GPU acceleration is working properly
"""

import torch
import time
from textureGenPipeline import MaterialMVPConfig

def test_gpu_setup():
    """Test GPU availability and configuration"""
    print("=== GPU Acceleration Test ===")
    
    # Test CUDA availability
    print(f"CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"GPU Count: {torch.cuda.device_count()}")
        print(f"Current GPU: {torch.cuda.current_device()}")
        print(f"GPU Name: {torch.cuda.get_device_name()}")
        
        # Test GPU memory
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"Total GPU Memory: {total_memory:.1f} GB")
        
        # Test tensor operations on GPU with larger matrices
        print("\n=== Testing GPU Tensor Operations ===")
        device = torch.device("cuda")

        # Create larger test tensors for better GPU utilization
        size = 4096  # Larger size to show GPU advantage
        a = torch.randn(size, size, device=device)
        b = torch.randn(size, size, device=device)

        # Warm up GPU
        _ = torch.matmul(a[:100, :100], b[:100, :100])
        torch.cuda.synchronize()

        # Time matrix multiplication on GPU
        start_time = time.time()
        c = torch.matmul(a, b)
        torch.cuda.synchronize()  # Wait for GPU operation to complete
        gpu_time = time.time() - start_time

        print(f"GPU Matrix Multiplication ({size}x{size}): {gpu_time:.4f}s")

        # Compare with CPU (smaller size to avoid long wait)
        size_cpu = 2048  # Smaller for CPU test
        a_cpu = torch.randn(size_cpu, size_cpu)
        b_cpu = torch.randn(size_cpu, size_cpu)
        start_time = time.time()
        c_cpu = torch.matmul(a_cpu, b_cpu)
        cpu_time = time.time() - start_time

        print(f"CPU Matrix Multiplication ({size_cpu}x{size_cpu}): {cpu_time:.4f}s")
        # Estimate GPU speedup (accounting for different matrix sizes)
        estimated_cpu_time = cpu_time * (size/size_cpu)**3  # O(n^3) complexity
        print(f"Estimated GPU Speedup: {estimated_cpu_time/gpu_time:.1f}x")
        
    else:
        print("❌ CUDA not available - GPU acceleration disabled")
        return False
    
    # Test MaterialMVP configuration
    print("\n=== Testing MaterialMVP Configuration ===")
    try:
        config = MaterialMVPConfig(max_num_view=6, resolution=512)
        print(f"✓ MaterialMVP Config created successfully")
        print(f"✓ Device: {config.device}")
        return True
    except Exception as e:
        print(f"❌ Error creating MaterialMVP Config: {e}")
        return False

def test_model_loading():
    """Test if models can be loaded on GPU"""
    print("\n=== Testing Model Loading ===")

    try:
        # Apply torchvision fix first
        try:
            from utils.torchvision_fix import apply_fix
            apply_fix()
            print("✓ Applied torchvision compatibility fix")
        except ImportError:
            print("⚠ torchvision_fix module not found, proceeding without fix")
        except Exception as e:
            print(f"⚠ Failed to apply torchvision fix: {e}")

        from utils.image_super_utils import imageSuperNet
        config = MaterialMVPConfig(max_num_view=6, resolution=512)

        print("Testing RealESRGAN model loading...")
        start_time = time.time()
        super_model = imageSuperNet(config)
        load_time = time.time() - start_time
        print(f"✓ RealESRGAN loaded in {load_time:.1f}s")

        # Test a small inference to verify GPU usage
        from PIL import Image
        import numpy as np
        test_img = Image.fromarray(np.random.randint(0, 255, (64, 64, 3), dtype=np.uint8))

        print("Testing super resolution inference...")
        start_time = time.time()
        enhanced = super_model(test_img)
        inference_time = time.time() - start_time
        print(f"✓ Super resolution inference completed in {inference_time:.2f}s")
        print(f"✓ Output size: {enhanced.size} (4x upscaling)")

        return True
    except Exception as e:
        print(f"❌ Error loading models: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    gpu_ok = test_gpu_setup()
    if gpu_ok:
        model_ok = test_model_loading()
        if model_ok:
            print("\n✅ All tests passed! GPU acceleration should work properly.")
        else:
            print("\n⚠️  GPU works but model loading failed.")
    else:
        print("\n❌ GPU acceleration not available.")
