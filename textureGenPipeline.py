import os
import torch
import copy
import trimesh
import numpy as np
from PIL import Image
from typing import List
from tqdm import tqdm
from DifferentiableRenderer.MeshRender import MeshRender
from utils.simplify_mesh_utils import remesh_mesh
from utils.multiview_utils import multiviewDiffusionNet
from utils.pipeline_utils import ViewProcessor
from utils.image_super_utils import imageSuperNet
from utils.uvwrap_utils import mesh_uv_wrap
from DifferentiableRenderer.mesh_utils import convert_obj_to_glb
import warnings

warnings.filterwarnings("ignore")
from diffusers.utils import logging as diffusers_logging

diffusers_logging.set_verbosity(50)


class MaterialMVPConfig:
    def __init__(self, max_num_view, resolution):
        # Verify CUDA availability and set device
        if torch.cuda.is_available():
            self.device = "cuda"
            print(f"✓ CUDA available - Using GPU: {torch.cuda.get_device_name()}")
            print(f"✓ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        else:
            self.device = "cpu"
            print("⚠ CUDA not available - Using CPU (will be very slow)")

        self.multiview_cfg_path = "cfgs/v1.yaml"

        self.multiview_pretrained_path = "tencent/Hunyuan3D-2.1"
        self.dino_ckpt_path = "facebook/dinov2-giant"
        self.realesrgan_ckpt_path = "ckpt/RealESRGAN_x4plus.pth"

        self.raster_mode = "cr"
        self.bake_mode = "back_sample"
        self.render_size = 1024 * 2
        self.texture_size = 1024 * 4
        self.max_selected_view_num = max_num_view
        self.resolution = resolution
        self.bake_exp = 4
        self.merge_method = "fast"

        # Performance optimizations
        self.save_intermediate_results = True  # Save intermediate results for debugging

        # view selection
        self.candidate_camera_azims = [0, 90, 180, 270, 0, 180]
        self.candidate_camera_elevs = [0, 0, 0, 0, 90, -90]
        self.candidate_view_weights = [1, 0.1, 0.5, 0.1, 0.05, 0.05]

        for azim in range(0, 360, 30):
            self.candidate_camera_azims.append(azim)
            self.candidate_camera_elevs.append(20)
            self.candidate_view_weights.append(0.01)

            self.candidate_camera_azims.append(azim)
            self.candidate_camera_elevs.append(-20)
            self.candidate_view_weights.append(0.01)


class MaterialMVPPipeline:

    def __init__(self, config=None) -> None:
        self.config = config if config is not None else MaterialMVPConfig(max_num_view=6, resolution=512)
        self.models = {}
        self.stats_logs = {}
        self.render = MeshRender(
            default_resolution=self.config.render_size,
            texture_size=self.config.texture_size,
            bake_mode=self.config.bake_mode,
            raster_mode=self.config.raster_mode,
        )
        self.view_processor = ViewProcessor(self.config, self.render)
        self.load_models()

    def load_models(self):
        print("Loading models...")
        torch.cuda.empty_cache()

        # Load models with progress tracking
        with tqdm(total=2, desc="Loading models", unit="model") as pbar:
            pbar.set_description("Loading super resolution model...")
            self.models["super_model"] = imageSuperNet(self.config)
            pbar.update(1)

            pbar.set_description("Loading multiview diffusion model...")
            self.models["multiview_model"] = multiviewDiffusionNet(self.config)
            pbar.update(1)

        print("✓ All models loaded successfully!")

        # Print GPU memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1e9
            memory_reserved = torch.cuda.memory_reserved() / 1e9
            print(f"GPU Memory - Allocated: {memory_allocated:.1f} GB, Reserved: {memory_reserved:.1f} GB")

    def save_intermediate_result(self, images, stage_name, output_dir):
        """Save intermediate results for debugging and progress visualization"""
        if not self.config.save_intermediate_results:
            return

        stage_dir = os.path.join(output_dir, "intermediate_results", stage_name)
        os.makedirs(stage_dir, exist_ok=True)

        if isinstance(images, dict):
            for key, img_list in images.items():
                for i, img in enumerate(img_list):
                    if hasattr(img, 'save'):  # PIL Image
                        img.save(os.path.join(stage_dir, f"{key}_{i:02d}.png"))
        elif isinstance(images, list):
            for i, img in enumerate(images):
                if hasattr(img, 'save'):  # PIL Image
                    img.save(os.path.join(stage_dir, f"image_{i:02d}.png"))
        print(f"✓ Saved intermediate results to: {stage_dir}")

    @torch.no_grad()
    def __call__(self, mesh_path=None, image_path=None, output_mesh_path=None, use_remesh=True, save_glb=True):
        """Generate texture for 3D mesh using multiview diffusion"""

        # Initialize overall progress bar
        total_steps = 8  # Major pipeline stages
        with tqdm(total=total_steps, desc="MaterialMVP Pipeline", unit="step") as pbar:

            # Step 1: Process input image
            pbar.set_description("Processing input image...")
            if isinstance(image_path, str):
                image_prompt = Image.open(image_path)
            elif isinstance(image_path, Image.Image):
                image_prompt = image_path
            if not isinstance(image_prompt, List):
                image_prompt = [image_prompt]
            else:
                image_prompt = image_path
            pbar.update(1)

            # Step 2: Process mesh
            pbar.set_description("Processing mesh...")
            path = os.path.dirname(mesh_path)
            if use_remesh:
                processed_mesh_path = os.path.join(path, "white_mesh_remesh.obj")
                remesh_mesh(mesh_path, processed_mesh_path)
            else:
                processed_mesh_path = mesh_path

            # Output path
            if output_mesh_path is None:
                output_mesh_path = os.path.join(path, "textured_mesh.obj")

            # Load mesh
            mesh = trimesh.load(processed_mesh_path)
            mesh = mesh_uv_wrap(mesh)
            self.render.load_mesh(mesh=mesh)
            pbar.update(1)

            # Step 3: View Selection
            pbar.set_description("Selecting optimal camera views...")
            selected_camera_elevs, selected_camera_azims, selected_view_weights = self.view_processor.bake_view_selection(
                self.config.candidate_camera_elevs,
                self.config.candidate_camera_azims,
                self.config.candidate_view_weights,
                self.config.max_selected_view_num,
            )
            pbar.update(1)

            # Step 4: Rendering views
            pbar.set_description("Rendering normal and position maps...")
            normal_maps = self.view_processor.render_normal_multiview(
                selected_camera_elevs, selected_camera_azims, use_abs_coor=True
            )
            position_maps = self.view_processor.render_position_multiview(selected_camera_elevs, selected_camera_azims)
            pbar.update(1)

            # Step 5: Style processing
            pbar.set_description("Processing style images...")
            image_caption = "high quality"
            image_style = []
            for image in image_prompt:
                image = image.resize((512, 512))
                if image.mode == "RGBA":
                    white_bg = Image.new("RGB", image.size, (255, 255, 255))
                    white_bg.paste(image, mask=image.getchannel("A"))
                    image = white_bg
                image_style.append(image)
            image_style = [image.convert("RGB") for image in image_style]
            pbar.update(1)

            # Step 6: Multiview diffusion generation
            pbar.set_description("Generating textures with diffusion model...")

            # Save normal and position maps
            self.save_intermediate_result(normal_maps, "01_normal_maps", path)
            self.save_intermediate_result(position_maps, "02_position_maps", path)

            multiviews_pbr = self.models["multiview_model"](
                image_style,
                normal_maps + position_maps,
                prompt=image_caption,
                custom_view_size=self.config.resolution,
                resize_input=True,
            )

            # Save generated multiview textures
            self.save_intermediate_result(multiviews_pbr, "03_multiview_textures", path)
            pbar.update(1)
            # Step 7: Enhancement with super resolution
            pbar.set_description("Enhancing textures with super resolution...")
            enhance_images = {}
            enhance_images["albedo"] = copy.deepcopy(multiviews_pbr["albedo"])
            enhance_images["mr"] = copy.deepcopy(multiviews_pbr["mr"])

            # Progress bar for enhancement with timing
            total_enhance = len(enhance_images["albedo"]) + len(enhance_images["mr"])
            with tqdm(total=total_enhance, desc="Super resolution", leave=False, unit="image") as enhance_pbar:
                import time
                start_time = time.time()

                for i in range(len(enhance_images["albedo"])):
                    img_start = time.time()
                    enhance_images["albedo"][i] = self.models["super_model"](enhance_images["albedo"][i])
                    img_time = time.time() - img_start
                    enhance_pbar.set_postfix({"img_time": f"{img_time:.1f}s"})
                    enhance_pbar.update(1)

                for i in range(len(enhance_images["mr"])):
                    img_start = time.time()
                    enhance_images["mr"][i] = self.models["super_model"](enhance_images["mr"][i])
                    img_time = time.time() - img_start
                    enhance_pbar.set_postfix({"img_time": f"{img_time:.1f}s"})
                    enhance_pbar.update(1)

                total_time = time.time() - start_time
                print(f"✓ Super resolution completed in {total_time:.1f}s")

            # Save enhanced textures
            self.save_intermediate_result(enhance_images, "04_enhanced_textures", path)
            pbar.update(1)

            # Step 8: Baking and final processing
            pbar.set_description("Baking textures and finalizing...")
            for i in range(len(enhance_images["albedo"])):
                enhance_images["albedo"][i] = enhance_images["albedo"][i].resize(
                    (self.config.render_size, self.config.render_size)
                )
                enhance_images["mr"][i] = enhance_images["mr"][i].resize((self.config.render_size, self.config.render_size))

            texture, mask = self.view_processor.bake_from_multiview(
                enhance_images["albedo"], selected_camera_elevs, selected_camera_azims, selected_view_weights
            )
            mask_np = (mask.squeeze(-1).cpu().numpy() * 255).astype(np.uint8)
            texture_mr, mask_mr = self.view_processor.bake_from_multiview(
                enhance_images["mr"], selected_camera_elevs, selected_camera_azims, selected_view_weights
            )
            mask_mr_np = (mask_mr.squeeze(-1).cpu().numpy() * 255).astype(np.uint8)

            # Inpainting and final processing
            texture = self.view_processor.texture_inpaint(texture, mask_np)
            self.render.set_texture(texture, force_set=True)
            if "mr" in enhance_images:
                texture_mr = self.view_processor.texture_inpaint(texture_mr, mask_mr_np)
                self.render.set_texture_mr(texture_mr)

            # Save final textures as intermediate results
            if hasattr(texture, 'cpu'):
                # Convert tensor to PIL Image for saving
                texture_pil = Image.fromarray((texture.cpu().numpy() * 255).astype(np.uint8))
                self.save_intermediate_result([texture_pil], "05_final_textures", path)

            # Save final mesh
            self.render.save_mesh(output_mesh_path, downsample=True)

            if save_glb:
                convert_obj_to_glb(output_mesh_path, output_mesh_path.replace(".obj", ".glb"))
                print(f"✓ GLB file saved: {output_mesh_path.replace('.obj', '.glb')}")

            pbar.update(1)
            pbar.set_description("✓ Texture generation complete!")

            # Final GPU memory report
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated() / 1e9
                print(f"Final GPU Memory Usage: {memory_allocated:.1f} GB")

        print(f"✓ Pipeline completed! Output: {output_mesh_path}")
        return output_mesh_path
