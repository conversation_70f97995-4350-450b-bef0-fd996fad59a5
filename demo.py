import time
import torch
from textureGenPipeline import MaterialM<PERSON><PERSON><PERSON>eline, MaterialMVPConfig

try:
    from utils.torchvision_fix import apply_fix
    apply_fix()
    print("✓ Applied torchvision compatibility fix")
except ImportError:
    print("Warning: torchvision_fix module not found, proceeding without compatibility fix")
except Exception as e:
    print(f"Warning: Failed to apply torchvision fix: {e}")


if __name__ == "__main__":
    print("=== MaterialMVP Demo with GPU Acceleration ===")

    # Configuration
    max_num_view = 6
    resolution = 512

    # Create configuration and pipeline
    conf = MaterialMVPConfig(max_num_view, resolution)
    pipe = MaterialMVPPipeline(conf)

    # Monitor GPU memory before processing
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated() / 1e9
        print(f"Initial GPU Memory: {initial_memory:.1f} GB")

    # Run the pipeline with timing
    print("\n🚀 Starting texture generation...")
    start_time = time.time()

    output_mesh_path = pipe(
        mesh_path="test_examples/mesh.glb",
        image_path="test_examples/image.png"
    )

    total_time = time.time() - start_time

    print(f"\n✅ Pipeline completed in {total_time:.1f} seconds!")
    print(f"📁 Output mesh: {output_mesh_path}")

    # Final GPU memory report
    if torch.cuda.is_available():
        final_memory = torch.cuda.memory_allocated() / 1e9
        peak_memory = torch.cuda.max_memory_allocated() / 1e9
        print(f"📊 GPU Memory - Final: {final_memory:.1f} GB, Peak: {peak_memory:.1f} GB")
