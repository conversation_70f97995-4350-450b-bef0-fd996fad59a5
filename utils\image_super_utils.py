import numpy as np
from PIL import Image


class imageSuperNet:
    def __init__(self, config) -> None:
        from realesrgan import RealESRGANer
        from basicsr.archs.rrdbnet_arch import RRDBNet
        import torch

        # Determine GPU ID for RealESRGAN
        gpu_id = 0 if torch.cuda.is_available() and config.device == "cuda" else None

        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
        upsampler = RealESRGANer(
            scale=4,
            model_path=config.realesrgan_ckpt_path,
            dni_weight=None,
            model=model,
            tile=512,  # Use tiling for better GPU memory management
            tile_pad=10,
            pre_pad=0,
            half=True,
            gpu_id=gpu_id,  # Enable GPU acceleration
        )
        self.upsampler = upsampler
        print(f"RealESRGAN initialized with GPU ID: {gpu_id}")

    def __call__(self, image):
        output, _ = self.upsampler.enhance(np.array(image))
        output = Image.fromarray(output)
        return output
